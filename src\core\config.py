# -*- coding: utf-8 -*-
"""
应用程序配置管理模块
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from PySide6.QtCore import QSettings, QStandardPaths


class AppConfig:
    """应用程序配置管理类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.settings = QSettings()
        self.config_dir = Path(QStandardPaths.writableLocation(
            QStandardPaths.StandardLocation.AppConfigLocation
        ))
        self.config_file = self.config_dir / "config.json"
        self._config_cache: Dict[str, Any] = {}
        self._load_default_config()
        self._load_user_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        self._config_cache = {
            # 界面配置
            "ui": {
                "theme": "light",  # light, dark
                "language": "zh_CN",
                "window_geometry": None,
                "window_state": None,
                "splitter_sizes": [200, 600, 200],
                "show_hidden_files": False,
                "show_system_files": False,
                "icon_size": 32,
                "view_mode": "list",  # list, grid, tree
            },
            
            # 性能配置
            "performance": {
                "max_cache_size": 1000,  # MB
                "max_index_entries": 100000,
                "lazy_load_threshold": 1000,
                "worker_threads": 4,
                "enable_file_preview": True,
                "preview_cache_size": 50,  # MB
            },
            
            # 文件操作配置
            "file_operations": {
                "confirm_delete": True,
                "confirm_overwrite": True,
                "use_trash": True,
                "copy_buffer_size": 1024 * 1024,  # 1MB
                "enable_progress_dialog": True,
            },
            
            # 搜索配置
            "search": {
                "enable_content_search": True,
                "max_search_results": 1000,
                "search_timeout": 30,  # seconds
                "index_file_types": [".txt", ".py", ".js", ".html", ".css", ".md"],
                "exclude_directories": [".git", "__pycache__", "node_modules"],
            },
            
            # 监控配置
            "monitoring": {
                "enable_file_watcher": True,
                "watch_subdirectories": True,
                "debounce_interval": 100,  # ms
            }
        }
    
    def _load_user_config(self):
        """加载用户配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(self._config_cache, user_config)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]):
        """合并配置字典"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config_cache
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self._config_cache
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def reset_to_default(self):
        """重置为默认配置"""
        self._load_default_config()
        self.save()


# 全局配置实例
app_config = AppConfig()
