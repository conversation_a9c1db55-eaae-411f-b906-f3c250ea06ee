#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能文件管理器主入口
基于PySide6的跨平台文件管理应用
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QDir, QStandardPaths
from PySide6.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ui.main_window import MainWindow
from src.core.config import AppConfig
from src.core.logger import setup_logger


def setup_application():
    """初始化应用程序配置"""
    # 设置应用程序属性
    QApplication.setApplicationName("高性能目录管理器")
    QApplication.setApplicationVersion("1.0.0")
    QApplication.setOrganizationName("FileManager")
    QApplication.setOrganizationDomain("filemanager.local")
    
    # 设置高DPI支持
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        QApplication.HighDpiScaleFactorRoundingPolicy.PassThrough
    )


def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序配置
    setup_application()
    
    # 初始化日志系统
    logger = setup_logger()
    logger.info("应用程序启动")
    
    # 加载应用配置
    config = AppConfig()
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 运行应用程序
    try:
        exit_code = app.exec()
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
    except Exception as e:
        logger.error(f"应用程序运行时发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
