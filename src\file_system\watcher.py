# -*- coding: utf-8 -*-
"""
文件系统监控模块 - 基于watchdog实现实时文件变化监控
"""

import os
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable, Set
from dataclasses import dataclass
from enum import Enum

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent
from watchdog.events import (
    FileCreatedEvent, FileDeletedEvent, FileModifiedEvent, 
    FileMovedEvent, DirCreatedEvent, DirDeletedEvent, DirMovedEvent
)

from ..core.logger import get_logger
from ..data_structures.btree import FileMetadata, FileIndex
from ..data_structures.lru_cache import LRUCache


class EventType(Enum):
    """文件系统事件类型"""
    CREATED = "created"
    DELETED = "deleted"
    MODIFIED = "modified"
    MOVED = "moved"


@dataclass
class FileChangeEvent:
    """文件变化事件"""
    event_type: EventType
    src_path: str
    dest_path: Optional[str] = None
    is_directory: bool = False
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()


class FileSystemWatcher(FileSystemEventHandler):
    """文件系统监控器"""
    
    def __init__(self, 
                 on_change: Optional[Callable[[FileChangeEvent], None]] = None,
                 debounce_interval: float = 0.1,
                 ignore_patterns: Optional[List[str]] = None):
        """
        初始化文件系统监控器
        
        Args:
            on_change: 文件变化回调函数
            debounce_interval: 防抖间隔（秒）
            ignore_patterns: 忽略的文件模式列表
        """
        super().__init__()
        self.on_change = on_change
        self.debounce_interval = debounce_interval
        self.ignore_patterns = ignore_patterns or [
            "*.tmp", "*.swp", "*.lock", "*~", ".DS_Store",
            "__pycache__", ".git", ".svn", "node_modules"
        ]
        
        # 防抖处理
        self._pending_events: Dict[str, FileChangeEvent] = {}
        self._debounce_timer: Optional[threading.Timer] = None
        self._lock = threading.Lock()
        
        self.logger = get_logger()
    
    def _should_ignore(self, path: str) -> bool:
        """检查是否应该忽略该路径"""
        path_obj = Path(path)
        
        # 检查文件名模式
        for pattern in self.ignore_patterns:
            if path_obj.match(pattern):
                return True
        
        # 检查路径中是否包含忽略的目录
        for part in path_obj.parts:
            for pattern in self.ignore_patterns:
                if part == pattern.replace("*", ""):
                    return True
        
        return False
    
    def _process_event(self, event: FileSystemEvent):
        """处理文件系统事件"""
        if self._should_ignore(event.src_path):
            return
        
        # 确定事件类型
        if isinstance(event, (FileCreatedEvent, DirCreatedEvent)):
            event_type = EventType.CREATED
        elif isinstance(event, (FileDeletedEvent, DirDeletedEvent)):
            event_type = EventType.DELETED
        elif isinstance(event, (FileModifiedEvent,)):
            event_type = EventType.MODIFIED
        elif isinstance(event, (FileMovedEvent, DirMovedEvent)):
            event_type = EventType.MOVED
        else:
            return
        
        # 创建变化事件
        change_event = FileChangeEvent(
            event_type=event_type,
            src_path=event.src_path,
            dest_path=getattr(event, 'dest_path', None),
            is_directory=event.is_directory
        )
        
        # 添加到防抖队列
        with self._lock:
            self._pending_events[event.src_path] = change_event
            
            # 重置防抖计时器
            if self._debounce_timer:
                self._debounce_timer.cancel()
            
            self._debounce_timer = threading.Timer(
                self.debounce_interval, 
                self._flush_pending_events
            )
            self._debounce_timer.start()
    
    def _flush_pending_events(self):
        """处理待处理的事件"""
        with self._lock:
            events = list(self._pending_events.values())
            self._pending_events.clear()
        
        # 处理事件
        for event in events:
            try:
                if self.on_change:
                    self.on_change(event)
            except Exception as e:
                self.logger.error(f"处理文件变化事件失败: {e}")
    
    def on_created(self, event):
        """文件/目录创建事件"""
        self._process_event(event)
    
    def on_deleted(self, event):
        """文件/目录删除事件"""
        self._process_event(event)
    
    def on_modified(self, event):
        """文件/目录修改事件"""
        if not event.is_directory:  # 只处理文件修改
            self._process_event(event)
    
    def on_moved(self, event):
        """文件/目录移动事件"""
        self._process_event(event)


class FileSystemMonitor:
    """文件系统监控管理器"""
    
    def __init__(self, file_index: FileIndex, cache: LRUCache):
        """
        初始化监控管理器
        
        Args:
            file_index: 文件索引
            cache: 缓存系统
        """
        self.file_index = file_index
        self.cache = cache
        self.observer = Observer()
        self.watched_paths: Set[str] = set()
        self.watchers: Dict[str, FileSystemWatcher] = {}
        self.is_running = False
        
        self.logger = get_logger()
    
    def add_watch_path(self, path: str, recursive: bool = True):
        """
        添加监控路径
        
        Args:
            path: 要监控的路径
            recursive: 是否递归监控子目录
        """
        if not os.path.exists(path):
            self.logger.warning(f"监控路径不存在: {path}")
            return
        
        if path in self.watched_paths:
            self.logger.info(f"路径已在监控中: {path}")
            return
        
        # 创建监控器
        watcher = FileSystemWatcher(
            on_change=self._handle_file_change,
            debounce_interval=0.1
        )
        
        # 添加监控
        self.observer.schedule(watcher, path, recursive=recursive)
        self.watched_paths.add(path)
        self.watchers[path] = watcher
        
        self.logger.info(f"添加监控路径: {path} (递归: {recursive})")
    
    def remove_watch_path(self, path: str):
        """
        移除监控路径
        
        Args:
            path: 要移除的路径
        """
        if path not in self.watched_paths:
            return
        
        # 移除监控
        # 注意：watchdog没有直接的unschedule方法，需要重新创建observer
        self.watched_paths.discard(path)
        if path in self.watchers:
            del self.watchers[path]
        
        self.logger.info(f"移除监控路径: {path}")
    
    def _handle_file_change(self, event: FileChangeEvent):
        """处理文件变化事件"""
        try:
            if event.event_type == EventType.CREATED:
                self._handle_file_created(event)
            elif event.event_type == EventType.DELETED:
                self._handle_file_deleted(event)
            elif event.event_type == EventType.MODIFIED:
                self._handle_file_modified(event)
            elif event.event_type == EventType.MOVED:
                self._handle_file_moved(event)
        except Exception as e:
            self.logger.error(f"处理文件变化失败: {e}")
    
    def _handle_file_created(self, event: FileChangeEvent):
        """处理文件创建事件"""
        if event.is_directory:
            return
        
        # 获取文件元数据
        metadata = self._get_file_metadata(event.src_path)
        if metadata:
            # 添加到索引
            self.file_index.add_file(metadata)
            self.logger.debug(f"文件已添加到索引: {event.src_path}")
    
    def _handle_file_deleted(self, event: FileChangeEvent):
        """处理文件删除事件"""
        if event.is_directory:
            return
        
        # 从索引中移除
        if self.file_index.remove_file(event.src_path):
            self.logger.debug(f"文件已从索引移除: {event.src_path}")
        
        # 从缓存中移除
        self.cache.remove(event.src_path)
    
    def _handle_file_modified(self, event: FileChangeEvent):
        """处理文件修改事件"""
        if event.is_directory:
            return
        
        # 更新索引
        metadata = self._get_file_metadata(event.src_path)
        if metadata:
            # 先删除旧记录，再添加新记录
            self.file_index.remove_file(event.src_path)
            self.file_index.add_file(metadata)
            
            # 清除缓存
            self.cache.remove(event.src_path)
            
            self.logger.debug(f"文件索引已更新: {event.src_path}")
    
    def _handle_file_moved(self, event: FileChangeEvent):
        """处理文件移动事件"""
        if event.is_directory or not event.dest_path:
            return
        
        # 从旧路径移除
        self.file_index.remove_file(event.src_path)
        self.cache.remove(event.src_path)
        
        # 添加新路径
        metadata = self._get_file_metadata(event.dest_path)
        if metadata:
            self.file_index.add_file(metadata)
            
        self.logger.debug(f"文件已移动: {event.src_path} -> {event.dest_path}")
    
    def _get_file_metadata(self, path: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return None
            
            stat = path_obj.stat()
            
            return FileMetadata(
                path=str(path_obj),
                size=stat.st_size,
                modified_time=stat.st_mtime,
                created_time=stat.st_ctime,
                file_type=path_obj.suffix.lower(),
                is_directory=path_obj.is_dir(),
                permissions=oct(stat.st_mode)[-3:]
            )
        except Exception as e:
            self.logger.error(f"获取文件元数据失败 {path}: {e}")
            return None
    
    def start(self):
        """启动监控"""
        if self.is_running:
            return
        
        self.observer.start()
        self.is_running = True
        self.logger.info("文件系统监控已启动")
    
    def stop(self):
        """停止监控"""
        if not self.is_running:
            return
        
        self.observer.stop()
        self.observer.join()
        self.is_running = False
        self.logger.info("文件系统监控已停止")
    
    def get_watched_paths(self) -> List[str]:
        """获取所有监控路径"""
        return list(self.watched_paths)
