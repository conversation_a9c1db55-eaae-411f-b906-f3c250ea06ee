# -*- coding: utf-8 -*-
"""
B+树索引实现 - 用于文件元数据的高效索引和范围查询
"""

import threading
import bisect
from typing import Any, Dict, List, Optional, Tuple, Iterator, Union
from dataclasses import dataclass
from pathlib import Path
import time


@dataclass
class FileMetadata:
    """文件元数据"""
    path: str
    size: int
    modified_time: float
    created_time: float
    file_type: str
    is_directory: bool
    permissions: str = ""
    checksum: str = ""


class BPlusTreeNode:
    """B+树节点基类"""
    
    def __init__(self, is_leaf: bool = False, max_keys: int = 100):
        """
        初始化节点
        
        Args:
            is_leaf: 是否为叶子节点
            max_keys: 最大键数量
        """
        self.is_leaf = is_leaf
        self.max_keys = max_keys
        self.keys: List[Any] = []
        self.parent: Optional['BPlusTreeNode'] = None
    
    def is_full(self) -> bool:
        """检查节点是否已满"""
        return len(self.keys) >= self.max_keys
    
    def is_underflow(self) -> bool:
        """检查节点是否下溢"""
        return len(self.keys) < self.max_keys // 2


class BPlusTreeLeaf(BPlusTreeNode):
    """B+树叶子节点"""
    
    def __init__(self, max_keys: int = 100):
        """初始化叶子节点"""
        super().__init__(is_leaf=True, max_keys=max_keys)
        self.values: List[FileMetadata] = []
        self.next_leaf: Optional['BPlusTreeLeaf'] = None
        self.prev_leaf: Optional['BPlusTreeLeaf'] = None
    
    def insert(self, key: Any, value: FileMetadata):
        """插入键值对"""
        pos = bisect.bisect_left(self.keys, key)
        self.keys.insert(pos, key)
        self.values.insert(pos, value)
    
    def remove(self, key: Any) -> bool:
        """移除键值对"""
        try:
            pos = self.keys.index(key)
            self.keys.pop(pos)
            self.values.pop(pos)
            return True
        except ValueError:
            return False
    
    def find(self, key: Any) -> Optional[FileMetadata]:
        """查找键对应的值"""
        try:
            pos = self.keys.index(key)
            return self.values[pos]
        except ValueError:
            return None
    
    def split(self) -> Tuple['BPlusTreeLeaf', Any]:
        """分裂叶子节点"""
        mid = len(self.keys) // 2
        
        # 创建新的右节点
        right_node = BPlusTreeLeaf(self.max_keys)
        right_node.keys = self.keys[mid:]
        right_node.values = self.values[mid:]
        right_node.parent = self.parent
        
        # 更新链表指针
        right_node.next_leaf = self.next_leaf
        right_node.prev_leaf = self
        if self.next_leaf:
            self.next_leaf.prev_leaf = right_node
        self.next_leaf = right_node
        
        # 截断当前节点
        self.keys = self.keys[:mid]
        self.values = self.values[:mid]
        
        # 返回新节点和上升的键
        return right_node, right_node.keys[0]


class BPlusTreeInternal(BPlusTreeNode):
    """B+树内部节点"""
    
    def __init__(self, max_keys: int = 100):
        """初始化内部节点"""
        super().__init__(is_leaf=False, max_keys=max_keys)
        self.children: List[BPlusTreeNode] = []
    
    def insert_child(self, key: Any, child: BPlusTreeNode):
        """插入子节点"""
        pos = bisect.bisect_left(self.keys, key)
        self.keys.insert(pos, key)
        self.children.insert(pos + 1, child)
        child.parent = self
    
    def remove_child(self, key: Any) -> bool:
        """移除子节点"""
        try:
            pos = self.keys.index(key)
            self.keys.pop(pos)
            child = self.children.pop(pos + 1)
            child.parent = None
            return True
        except ValueError:
            return False
    
    def find_child(self, key: Any) -> BPlusTreeNode:
        """查找应该包含指定键的子节点"""
        pos = bisect.bisect_right(self.keys, key)
        return self.children[pos]
    
    def split(self) -> Tuple['BPlusTreeInternal', Any]:
        """分裂内部节点"""
        mid = len(self.keys) // 2
        
        # 创建新的右节点
        right_node = BPlusTreeInternal(self.max_keys)
        
        # 上升的键
        up_key = self.keys[mid]
        
        # 分配键和子节点
        right_node.keys = self.keys[mid + 1:]
        right_node.children = self.children[mid + 1:]
        right_node.parent = self.parent
        
        # 更新子节点的父指针
        for child in right_node.children:
            child.parent = right_node
        
        # 截断当前节点
        self.keys = self.keys[:mid]
        self.children = self.children[:mid + 1]
        
        return right_node, up_key


class BPlusTree:
    """
    B+树索引实现
    支持高效的插入、删除、查找和范围查询
    """
    
    def __init__(self, max_keys: int = 100):
        """
        初始化B+树
        
        Args:
            max_keys: 每个节点的最大键数量
        """
        self.max_keys = max_keys
        self.root: BPlusTreeNode = BPlusTreeLeaf(max_keys)
        self._lock = threading.RLock()
        self._size = 0
    
    def insert(self, key: Any, value: FileMetadata):
        """
        插入键值对
        
        Args:
            key: 索引键
            value: 文件元数据
        """
        with self._lock:
            self._insert(key, value)
            self._size += 1
    
    def _insert(self, key: Any, value: FileMetadata):
        """内部插入方法"""
        leaf = self._find_leaf(key)
        leaf.insert(key, value)
        
        if leaf.is_full():
            self._split_leaf(leaf)
    
    def _find_leaf(self, key: Any) -> BPlusTreeLeaf:
        """查找应该包含指定键的叶子节点"""
        node = self.root
        
        while not node.is_leaf:
            internal = node
            node = internal.find_child(key)
        
        return node
    
    def _split_leaf(self, leaf: BPlusTreeLeaf):
        """分裂叶子节点并向上传播"""
        right_leaf, up_key = leaf.split()
        
        if leaf.parent is None:
            # 根节点分裂，创建新根
            new_root = BPlusTreeInternal(self.max_keys)
            new_root.keys.append(up_key)
            new_root.children.extend([leaf, right_leaf])
            leaf.parent = new_root
            right_leaf.parent = new_root
            self.root = new_root
        else:
            # 向父节点插入新键
            parent = leaf.parent
            parent.insert_child(up_key, right_leaf)
            
            if parent.is_full():
                self._split_internal(parent)
    
    def _split_internal(self, internal: BPlusTreeInternal):
        """分裂内部节点并向上传播"""
        right_internal, up_key = internal.split()
        
        if internal.parent is None:
            # 根节点分裂，创建新根
            new_root = BPlusTreeInternal(self.max_keys)
            new_root.keys.append(up_key)
            new_root.children.extend([internal, right_internal])
            internal.parent = new_root
            right_internal.parent = new_root
            self.root = new_root
        else:
            # 向父节点插入新键
            parent = internal.parent
            parent.insert_child(up_key, right_internal)
            
            if parent.is_full():
                self._split_internal(parent)
    
    def find(self, key: Any) -> Optional[FileMetadata]:
        """
        查找指定键的值
        
        Args:
            key: 要查找的键
            
        Returns:
            文件元数据，如果不存在则返回None
        """
        with self._lock:
            leaf = self._find_leaf(key)
            return leaf.find(key)
    
    def remove(self, key: Any) -> bool:
        """
        移除指定键
        
        Args:
            key: 要移除的键
            
        Returns:
            是否成功移除
        """
        with self._lock:
            leaf = self._find_leaf(key)
            if leaf.remove(key):
                self._size -= 1
                
                # 检查是否需要合并或重新分布
                if leaf.is_underflow() and leaf != self.root:
                    self._handle_underflow(leaf)
                
                return True
            return False
    
    def _handle_underflow(self, node: BPlusTreeNode):
        """处理节点下溢"""
        # 简化实现：这里可以添加更复杂的合并和重新分布逻辑
        pass
    
    def range_query(self, start_key: Any, end_key: Any, include_start: bool = True, 
                   include_end: bool = True) -> List[FileMetadata]:
        """
        范围查询
        
        Args:
            start_key: 起始键
            end_key: 结束键
            include_start: 是否包含起始键
            include_end: 是否包含结束键
            
        Returns:
            范围内的所有文件元数据
        """
        with self._lock:
            results = []
            
            # 找到起始叶子节点
            leaf = self._find_leaf(start_key)
            
            # 遍历叶子节点链表
            while leaf:
                for i, key in enumerate(leaf.keys):
                    # 检查是否在范围内
                    if key < start_key or (key == start_key and not include_start):
                        continue
                    if key > end_key or (key == end_key and not include_end):
                        return results
                    
                    results.append(leaf.values[i])
                
                leaf = leaf.next_leaf
            
            return results

    def get_all_in_order(self) -> List[FileMetadata]:
        """按键顺序获取所有值"""
        with self._lock:
            results = []

            # 找到最左边的叶子节点
            node = self.root
            while not node.is_leaf:
                node = node.children[0]

            # 遍历所有叶子节点
            leaf = node
            while leaf:
                results.extend(leaf.values)
                leaf = leaf.next_leaf

            return results

    def size(self) -> int:
        """返回树中的元素数量"""
        return self._size

    def is_empty(self) -> bool:
        """检查树是否为空"""
        return self._size == 0

    def clear(self):
        """清空树"""
        with self._lock:
            self.root = BPlusTreeLeaf(self.max_keys)
            self._size = 0

    def get_stats(self) -> Dict[str, Any]:
        """获取树的统计信息"""
        with self._lock:
            stats = {
                'size': self._size,
                'height': self._get_height(),
                'leaf_count': self._count_leaves(),
                'internal_count': self._count_internal_nodes()
            }
            return stats

    def _get_height(self) -> int:
        """计算树的高度"""
        height = 0
        node = self.root

        while not node.is_leaf:
            height += 1
            node = node.children[0]

        return height + 1

    def _count_leaves(self) -> int:
        """计算叶子节点数量"""
        count = 0

        # 找到最左边的叶子节点
        node = self.root
        while not node.is_leaf:
            node = node.children[0]

        # 遍历所有叶子节点
        leaf = node
        while leaf:
            count += 1
            leaf = leaf.next_leaf

        return count

    def _count_internal_nodes(self) -> int:
        """计算内部节点数量"""
        if self.root.is_leaf:
            return 0

        count = 0
        queue = [self.root]

        while queue:
            node = queue.pop(0)
            if not node.is_leaf:
                count += 1
                queue.extend(node.children)

        return count


class FileIndex:
    """
    文件索引管理器
    使用多个B+树为不同属性建立索引
    """

    def __init__(self, max_keys: int = 100):
        """初始化文件索引"""
        self.max_keys = max_keys

        # 不同属性的索引
        self.path_index = BPlusTree(max_keys)      # 按路径索引
        self.size_index = BPlusTree(max_keys)      # 按大小索引
        self.time_index = BPlusTree(max_keys)      # 按修改时间索引
        self.type_index = BPlusTree(max_keys)      # 按文件类型索引

        self._lock = threading.RLock()

    def add_file(self, metadata: FileMetadata):
        """添加文件到索引"""
        with self._lock:
            self.path_index.insert(metadata.path, metadata)
            self.size_index.insert(metadata.size, metadata)
            self.time_index.insert(metadata.modified_time, metadata)
            self.type_index.insert(metadata.file_type, metadata)

    def remove_file(self, path: str) -> bool:
        """从索引中移除文件"""
        with self._lock:
            # 先查找文件元数据
            metadata = self.path_index.find(path)
            if not metadata:
                return False

            # 从所有索引中移除
            self.path_index.remove(path)
            self.size_index.remove(metadata.size)
            self.time_index.remove(metadata.modified_time)
            self.type_index.remove(metadata.file_type)

            return True

    def find_by_path(self, path: str) -> Optional[FileMetadata]:
        """按路径查找文件"""
        return self.path_index.find(path)

    def find_by_size_range(self, min_size: int, max_size: int) -> List[FileMetadata]:
        """按大小范围查找文件"""
        return self.size_index.range_query(min_size, max_size)

    def find_by_time_range(self, start_time: float, end_time: float) -> List[FileMetadata]:
        """按时间范围查找文件"""
        return self.time_index.range_query(start_time, end_time)

    def find_by_type(self, file_type: str) -> List[FileMetadata]:
        """按文件类型查找文件"""
        return self.type_index.range_query(file_type, file_type)

    def get_all_files(self) -> List[FileMetadata]:
        """获取所有文件"""
        return self.path_index.get_all_in_order()

    def get_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        with self._lock:
            return {
                'total_files': self.path_index.size(),
                'path_index_stats': self.path_index.get_stats(),
                'size_index_stats': self.size_index.get_stats(),
                'time_index_stats': self.time_index.get_stats(),
                'type_index_stats': self.type_index.get_stats()
            }

    def clear(self):
        """清空所有索引"""
        with self._lock:
            self.path_index.clear()
            self.size_index.clear()
            self.time_index.clear()
            self.type_index.clear()
