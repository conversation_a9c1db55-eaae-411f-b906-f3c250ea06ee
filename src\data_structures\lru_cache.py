# -*- coding: utf-8 -*-
"""
LRU缓存实现 - 用于文件信息和预览数据的高效缓存
"""

import threading
import time
from typing import Any, Dict, Optional, Tuple, Iterator
from collections import OrderedDict


class LRUNode:
    """LRU缓存节点"""
    
    def __init__(self, key: str, value: Any, size: int = 1):
        """
        初始化节点
        
        Args:
            key: 键
            value: 值
            size: 数据大小（字节）
        """
        self.key = key
        self.value = value
        self.size = size
        self.access_time = time.time()
        self.access_count = 1
        self.prev: Optional['LRUNode'] = None
        self.next: Optional['LRUNode'] = None


class LRUCache:
    """
    线程安全的LRU缓存实现
    支持大小限制、TTL过期和统计信息
    """
    
    def __init__(self, max_size: int = 1000, max_memory: int = 100 * 1024 * 1024, ttl: Optional[float] = None):
        """
        初始化LRU缓存
        
        Args:
            max_size: 最大条目数
            max_memory: 最大内存使用量（字节）
            ttl: 生存时间（秒），None表示不过期
        """
        self.max_size = max_size
        self.max_memory = max_memory
        self.ttl = ttl
        
        # 双向链表头尾节点
        self.head = LRUNode("", None)
        self.tail = LRUNode("", None)
        self.head.next = self.tail
        self.tail.prev = self.head
        
        # 哈希表用于O(1)查找
        self.cache: Dict[str, LRUNode] = {}
        
        # 统计信息
        self.current_size = 0
        self.current_memory = 0
        self.hit_count = 0
        self.miss_count = 0
        
        # 线程锁
        self._lock = threading.RLock()
    
    def _add_to_head(self, node: LRUNode):
        """将节点添加到头部"""
        node.prev = self.head
        node.next = self.head.next
        self.head.next.prev = node
        self.head.next = node
    
    def _remove_node(self, node: LRUNode):
        """从链表中移除节点"""
        node.prev.next = node.next
        node.next.prev = node.prev
    
    def _move_to_head(self, node: LRUNode):
        """将节点移动到头部"""
        self._remove_node(node)
        self._add_to_head(node)
    
    def _pop_tail(self) -> LRUNode:
        """移除尾部节点"""
        last_node = self.tail.prev
        self._remove_node(last_node)
        return last_node
    
    def _is_expired(self, node: LRUNode) -> bool:
        """检查节点是否过期"""
        if self.ttl is None:
            return False
        return time.time() - node.access_time > self.ttl
    
    def _evict_expired(self):
        """清理过期节点"""
        if self.ttl is None:
            return
        
        current_time = time.time()
        expired_keys = []
        
        for key, node in self.cache.items():
            if current_time - node.access_time > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_key(key)
    
    def _remove_key(self, key: str):
        """内部移除键方法"""
        if key in self.cache:
            node = self.cache[key]
            self._remove_node(node)
            del self.cache[key]
            self.current_size -= 1
            self.current_memory -= node.size
    
    def _evict_lru(self):
        """清理LRU节点直到满足大小和内存限制"""
        while (self.current_size > self.max_size or 
               self.current_memory > self.max_memory) and self.current_size > 0:
            tail_node = self._pop_tail()
            if tail_node.key in self.cache:
                del self.cache[tail_node.key]
                self.current_size -= 1
                self.current_memory -= tail_node.size
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 键
            
        Returns:
            缓存的值，如果不存在或过期则返回None
        """
        with self._lock:
            if key not in self.cache:
                self.miss_count += 1
                return None
            
            node = self.cache[key]
            
            # 检查是否过期
            if self._is_expired(node):
                self._remove_key(key)
                self.miss_count += 1
                return None
            
            # 更新访问信息
            node.access_time = time.time()
            node.access_count += 1
            
            # 移动到头部
            self._move_to_head(node)
            
            self.hit_count += 1
            return node.value
    
    def put(self, key: str, value: Any, size: Optional[int] = None):
        """
        设置缓存值
        
        Args:
            key: 键
            value: 值
            size: 数据大小（字节），如果为None则尝试估算
        """
        if size is None:
            size = self._estimate_size(value)
        
        with self._lock:
            if key in self.cache:
                # 更新现有节点
                node = self.cache[key]
                old_size = node.size
                node.value = value
                node.size = size
                node.access_time = time.time()
                node.access_count += 1
                
                self.current_memory = self.current_memory - old_size + size
                self._move_to_head(node)
            else:
                # 创建新节点
                node = LRUNode(key, value, size)
                self.cache[key] = node
                self._add_to_head(node)
                
                self.current_size += 1
                self.current_memory += size
            
            # 清理过期节点
            self._evict_expired()
            
            # 清理LRU节点
            self._evict_lru()
    
    def _estimate_size(self, value: Any) -> int:
        """估算对象大小"""
        try:
            import sys
            return sys.getsizeof(value)
        except:
            # 简单估算
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (list, tuple)):
                return sum(self._estimate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) 
                          for k, v in value.items())
            else:
                return 64  # 默认大小
    
    def remove(self, key: str) -> bool:
        """
        移除缓存项
        
        Args:
            key: 要移除的键
            
        Returns:
            是否成功移除
        """
        with self._lock:
            if key in self.cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.head.next = self.tail
            self.tail.prev = self.head
            self.current_size = 0
            self.current_memory = 0
    
    def keys(self) -> Iterator[str]:
        """获取所有键的迭代器"""
        with self._lock:
            return iter(list(self.cache.keys()))
    
    def values(self) -> Iterator[Any]:
        """获取所有值的迭代器"""
        with self._lock:
            return iter([node.value for node in self.cache.values()])
    
    def items(self) -> Iterator[Tuple[str, Any]]:
        """获取所有键值对的迭代器"""
        with self._lock:
            return iter([(key, node.value) for key, node in self.cache.items()])
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            
            return {
                'size': self.current_size,
                'max_size': self.max_size,
                'memory_usage': self.current_memory,
                'max_memory': self.max_memory,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate,
                'memory_usage_mb': self.current_memory / (1024 * 1024)
            }
    
    def get_most_accessed(self, limit: int = 10) -> List[Tuple[str, int]]:
        """获取访问次数最多的键"""
        with self._lock:
            items = [(key, node.access_count) for key, node in self.cache.items()]
            items.sort(key=lambda x: x[1], reverse=True)
            return items[:limit]
    
    def resize(self, max_size: int, max_memory: int):
        """调整缓存大小限制"""
        with self._lock:
            self.max_size = max_size
            self.max_memory = max_memory
            self._evict_lru()
    
    def __len__(self) -> int:
        """返回缓存大小"""
        return self.current_size
    
    def __contains__(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key not in self.cache:
                return False
            
            node = self.cache[key]
            return not self._is_expired(node)
    
    def __getitem__(self, key: str) -> Any:
        """字典式访问"""
        value = self.get(key)
        if value is None:
            raise KeyError(key)
        return value
    
    def __setitem__(self, key: str, value: Any):
        """字典式设置"""
        self.put(key, value)
    
    def __delitem__(self, key: str):
        """字典式删除"""
        if not self.remove(key):
            raise KeyError(key)
