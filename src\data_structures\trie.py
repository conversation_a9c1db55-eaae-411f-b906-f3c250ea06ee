# -*- coding: utf-8 -*-
"""
Trie树实现 - 用于高效的文件名前缀搜索
"""

from typing import Dict, List, Optional, Set, Tuple
import threading
from pathlib import Path


class TrieNode:
    """Trie树节点"""
    
    def __init__(self):
        """初始化节点"""
        self.children: Dict[str, 'TrieNode'] = {}
        self.is_end_of_word: bool = False
        self.file_paths: Set[str] = set()  # 存储完整文件路径
        self.frequency: int = 0  # 访问频率，用于排序
    
    def add_path(self, path: str):
        """添加文件路径"""
        self.file_paths.add(path)
        self.frequency += 1
    
    def remove_path(self, path: str):
        """移除文件路径"""
        self.file_paths.discard(path)
        if not self.file_paths:
            self.frequency = 0


class Trie:
    """
    Trie树实现，用于文件名的快速前缀搜索
    支持线程安全操作和增量更新
    """
    
    def __init__(self, case_sensitive: bool = False):
        """
        初始化Trie树
        
        Args:
            case_sensitive: 是否区分大小写
        """
        self.root = TrieNode()
        self.case_sensitive = case_sensitive
        self._lock = threading.RWLock()  # 读写锁
        self._size = 0
    
    def _normalize_key(self, key: str) -> str:
        """标准化键值"""
        return key if self.case_sensitive else key.lower()
    
    def insert(self, file_path: str):
        """
        插入文件路径
        
        Args:
            file_path: 文件完整路径
        """
        path_obj = Path(file_path)
        filename = path_obj.name
        
        with self._lock.writer():
            self._insert_word(filename, file_path)
            self._size += 1
    
    def _insert_word(self, word: str, file_path: str):
        """内部插入方法"""
        word = self._normalize_key(word)
        node = self.root
        
        for char in word:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
            node.add_path(file_path)
        
        node.is_end_of_word = True
    
    def remove(self, file_path: str):
        """
        移除文件路径
        
        Args:
            file_path: 要移除的文件路径
        """
        path_obj = Path(file_path)
        filename = path_obj.name
        
        with self._lock.writer():
            if self._remove_word(filename, file_path):
                self._size -= 1
    
    def _remove_word(self, word: str, file_path: str) -> bool:
        """内部移除方法"""
        word = self._normalize_key(word)
        
        def _remove_recursive(node: TrieNode, word: str, index: int) -> bool:
            if index == len(word):
                if not node.is_end_of_word:
                    return False
                
                node.remove_path(file_path)
                if not node.file_paths:
                    node.is_end_of_word = False
                
                # 如果节点没有子节点且不是单词结尾，可以删除
                return len(node.children) == 0 and not node.is_end_of_word
            
            char = word[index]
            if char not in node.children:
                return False
            
            should_delete_child = _remove_recursive(node.children[char], word, index + 1)
            
            if should_delete_child:
                del node.children[char]
            
            node.remove_path(file_path)
            
            # 当前节点可以删除的条件：没有子节点，不是单词结尾，没有文件路径
            return (len(node.children) == 0 and 
                   not node.is_end_of_word and 
                   len(node.file_paths) == 0)
        
        return _remove_recursive(self.root, word, 0)
    
    def search_prefix(self, prefix: str, max_results: int = 100) -> List[Tuple[str, int]]:
        """
        搜索指定前缀的所有文件
        
        Args:
            prefix: 搜索前缀
            max_results: 最大结果数量
            
        Returns:
            List[Tuple[str, int]]: (文件路径, 频率) 的列表，按频率降序排列
        """
        prefix = self._normalize_key(prefix)
        
        with self._lock.reader():
            # 找到前缀对应的节点
            node = self.root
            for char in prefix:
                if char not in node.children:
                    return []
                node = node.children[char]
            
            # 收集所有匹配的文件路径
            results = []
            self._collect_paths(node, results)
            
            # 按频率排序并限制结果数量
            results.sort(key=lambda x: x[1], reverse=True)
            return results[:max_results]
    
    def _collect_paths(self, node: TrieNode, results: List[Tuple[str, int]]):
        """递归收集路径"""
        # 添加当前节点的所有文件路径
        for path in node.file_paths:
            results.append((path, node.frequency))
        
        # 递归处理子节点
        for child in node.children.values():
            self._collect_paths(child, results)
    
    def fuzzy_search(self, pattern: str, max_distance: int = 2, max_results: int = 100) -> List[Tuple[str, int, int]]:
        """
        模糊搜索（基于编辑距离）
        
        Args:
            pattern: 搜索模式
            max_distance: 最大编辑距离
            max_results: 最大结果数量
            
        Returns:
            List[Tuple[str, int, int]]: (文件路径, 频率, 编辑距离) 的列表
        """
        pattern = self._normalize_key(pattern)
        results = []
        
        with self._lock.reader():
            self._fuzzy_search_recursive(
                self.root, pattern, "", 0, max_distance, results
            )
        
        # 按编辑距离和频率排序
        results.sort(key=lambda x: (x[2], -x[1]))
        return results[:max_results]
    
    def _fuzzy_search_recursive(self, node: TrieNode, pattern: str, current: str, 
                               distance: int, max_distance: int, results: List[Tuple[str, int, int]]):
        """递归模糊搜索"""
        if distance > max_distance:
            return
        
        # 如果到达模式末尾，检查是否有匹配的文件
        if len(current) >= len(pattern):
            for path in node.file_paths:
                edit_dist = self._edit_distance(Path(path).name.lower(), pattern)
                if edit_dist <= max_distance:
                    results.append((path, node.frequency, edit_dist))
            return
        
        # 继续搜索子节点
        for char, child in node.children.items():
            # 匹配字符
            if len(current) < len(pattern) and char == pattern[len(current)]:
                self._fuzzy_search_recursive(
                    child, pattern, current + char, distance, max_distance, results
                )
            else:
                # 插入、删除、替换操作
                self._fuzzy_search_recursive(
                    child, pattern, current + char, distance + 1, max_distance, results
                )
    
    def _edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
        
        return dp[m][n]
    
    def get_suggestions(self, partial: str, max_suggestions: int = 10) -> List[str]:
        """
        获取自动补全建议
        
        Args:
            partial: 部分输入
            max_suggestions: 最大建议数量
            
        Returns:
            List[str]: 建议的完整文件名列表
        """
        suggestions = []
        prefix_results = self.search_prefix(partial, max_suggestions * 2)
        
        seen_names = set()
        for path, _ in prefix_results:
            filename = Path(path).name
            if filename not in seen_names:
                suggestions.append(filename)
                seen_names.add(filename)
                
                if len(suggestions) >= max_suggestions:
                    break
        
        return suggestions
    
    def size(self) -> int:
        """返回Trie树中的文件数量"""
        with self._lock.reader():
            return self._size
    
    def clear(self):
        """清空Trie树"""
        with self._lock.writer():
            self.root = TrieNode()
            self._size = 0


# 线程安全的读写锁实现
class RWLock:
    """读写锁实现"""
    
    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0
    
    def reader(self):
        """获取读锁"""
        return self._ReaderLock(self)
    
    def writer(self):
        """获取写锁"""
        return self._WriterLock(self)
    
    class _ReaderLock:
        def __init__(self, rwlock):
            self.rwlock = rwlock
        
        def __enter__(self):
            self.rwlock._read_ready.acquire()
            try:
                self.rwlock._readers += 1
            finally:
                self.rwlock._read_ready.release()
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.rwlock._read_ready.acquire()
            try:
                self.rwlock._readers -= 1
                if self.rwlock._readers == 0:
                    self.rwlock._read_ready.notifyAll()
            finally:
                self.rwlock._read_ready.release()
    
    class _WriterLock:
        def __init__(self, rwlock):
            self.rwlock = rwlock
        
        def __enter__(self):
            self.rwlock._read_ready.acquire()
            while self.rwlock._readers > 0:
                self.rwlock._read_ready.wait()
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.rwlock._read_ready.release()


# 为threading模块添加RWLock
threading.RWLock = RWLock
