# -*- coding: utf-8 -*-
"""
主窗口UI模块 - 实现三栏式布局的文件管理器主界面
"""

import os
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStatusBar, QLabel, QProgressBar,
    QMessageBox, QFileDialog, QApplication
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, pyqtSignal
from PySide6.QtGui import QAction, QIcon, QKeySequence

from ..core.config import app_config
from ..core.logger import get_logger
from ..data_structures.btree import FileIndex
from ..data_structures.lru_cache import LRUCache
from ..file_system.watcher import FileSystemMonitor
from ..file_system.operations import FileOperations

from .directory_tree import DirectoryTreeWidget
from .file_list import FileListWidget
from .preview_panel import PreviewPanel
from .toolbar import MainToolBar
from .status_bar import MainStatusBar


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    file_selected = Signal(str)  # 文件选择信号
    directory_changed = Signal(str)  # 目录变化信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """初始化主窗口"""
        super().__init__(parent)
        
        self.logger = get_logger()
        
        # 初始化核心组件
        self.file_index = FileIndex()
        self.cache = LRUCache(
            max_size=app_config.get("performance.max_cache_size", 1000),
            max_memory=app_config.get("performance.preview_cache_size", 50) * 1024 * 1024
        )
        self.file_operations = FileOperations()
        self.file_monitor = FileSystemMonitor(self.file_index, self.cache)
        
        # UI组件
        self.directory_tree: Optional[DirectoryTreeWidget] = None
        self.file_list: Optional[FileListWidget] = None
        self.preview_panel: Optional[PreviewPanel] = None
        self.main_toolbar: Optional[MainToolBar] = None
        self.main_status_bar: Optional[MainStatusBar] = None
        
        # 当前路径
        self.current_path = str(Path.home())
        
        # 初始化UI
        self._setup_ui()
        self._setup_connections()
        self._load_settings()
        
        # 启动文件监控
        self._start_file_monitoring()
        
        self.logger.info("主窗口初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("高性能文件管理器")
        self.setMinimumSize(1200, 800)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建工具栏
        self.main_toolbar = MainToolBar(self)
        self.addToolBar(self.main_toolbar)
        
        # 创建三栏式分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：目录树
        self.directory_tree = DirectoryTreeWidget(self)
        self.directory_tree.setMinimumWidth(200)
        splitter.addWidget(self.directory_tree)
        
        # 中间：文件列表
        self.file_list = FileListWidget(self)
        self.file_list.setMinimumWidth(400)
        splitter.addWidget(self.file_list)
        
        # 右侧：预览面板
        self.preview_panel = PreviewPanel(self)
        self.preview_panel.setMinimumWidth(300)
        splitter.addWidget(self.preview_panel)
        
        # 设置分割器比例
        splitter.setSizes([250, 600, 350])
        splitter.setStretchFactor(0, 0)  # 目录树不拉伸
        splitter.setStretchFactor(1, 1)  # 文件列表可拉伸
        splitter.setStretchFactor(2, 0)  # 预览面板不拉伸
        
        # 创建状态栏
        self.main_status_bar = MainStatusBar(self)
        self.setStatusBar(self.main_status_bar)
        
        # 创建菜单栏
        self._create_menu_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建文件夹
        new_folder_action = QAction("新建文件夹(&N)", self)
        new_folder_action.setShortcut(QKeySequence.StandardKey.New)
        new_folder_action.triggered.connect(self._create_new_folder)
        file_menu.addAction(new_folder_action)
        
        file_menu.addSeparator()
        
        # 复制
        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.StandardKey.Copy)
        copy_action.triggered.connect(self._copy_selected)
        file_menu.addAction(copy_action)
        
        # 剪切
        cut_action = QAction("剪切(&X)", self)
        cut_action.setShortcut(QKeySequence.StandardKey.Cut)
        cut_action.triggered.connect(self._cut_selected)
        file_menu.addAction(cut_action)
        
        # 粘贴
        paste_action = QAction("粘贴(&V)", self)
        paste_action.setShortcut(QKeySequence.StandardKey.Paste)
        paste_action.triggered.connect(self._paste)
        file_menu.addAction(paste_action)
        
        file_menu.addSeparator()
        
        # 删除
        delete_action = QAction("删除(&D)", self)
        delete_action.setShortcut(QKeySequence.StandardKey.Delete)
        delete_action.triggered.connect(self._delete_selected)
        file_menu.addAction(delete_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&Q)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 全选
        select_all_action = QAction("全选(&A)", self)
        select_all_action.setShortcut(QKeySequence.StandardKey.SelectAll)
        select_all_action.triggered.connect(self._select_all)
        edit_menu.addAction(select_all_action)
        
        # 反选
        invert_selection_action = QAction("反选(&I)", self)
        invert_selection_action.triggered.connect(self._invert_selection)
        edit_menu.addAction(invert_selection_action)
        
        edit_menu.addSeparator()
        
        # 查找
        find_action = QAction("查找(&F)", self)
        find_action.setShortcut(QKeySequence.StandardKey.Find)
        find_action.triggered.connect(self._show_search)
        edit_menu.addAction(find_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 显示隐藏文件
        show_hidden_action = QAction("显示隐藏文件(&H)", self)
        show_hidden_action.setCheckable(True)
        show_hidden_action.setChecked(app_config.get("ui.show_hidden_files", False))
        show_hidden_action.triggered.connect(self._toggle_hidden_files)
        view_menu.addAction(show_hidden_action)
        
        # 刷新
        refresh_action = QAction("刷新(&R)", self)
        refresh_action.setShortcut(QKeySequence.StandardKey.Refresh)
        refresh_action.triggered.connect(self._refresh_view)
        view_menu.addAction(refresh_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 选项
        options_action = QAction("选项(&O)", self)
        options_action.triggered.connect(self._show_options)
        tools_menu.addAction(options_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 目录树信号
        if self.directory_tree:
            self.directory_tree.directory_selected.connect(self._on_directory_selected)
        
        # 文件列表信号
        if self.file_list:
            self.file_list.file_selected.connect(self._on_file_selected)
            self.file_list.directory_entered.connect(self._on_directory_entered)
        
        # 工具栏信号
        if self.main_toolbar:
            self.main_toolbar.navigation_requested.connect(self._navigate_to)
            self.main_toolbar.search_requested.connect(self._perform_search)
    
    def _load_settings(self):
        """加载设置"""
        # 恢复窗口几何形状
        geometry = app_config.get("ui.window_geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # 恢复窗口状态
        state = app_config.get("ui.window_state")
        if state:
            self.restoreState(state)
    
    def _save_settings(self):
        """保存设置"""
        # 保存窗口几何形状
        app_config.set("ui.window_geometry", self.saveGeometry())
        
        # 保存窗口状态
        app_config.set("ui.window_state", self.saveState())
        
        # 保存配置
        app_config.save()
    
    def _start_file_monitoring(self):
        """启动文件监控"""
        try:
            # 添加常用目录到监控
            common_paths = [
                str(Path.home()),
                str(Path.home() / "Desktop"),
                str(Path.home() / "Documents"),
                str(Path.home() / "Downloads")
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    self.file_monitor.add_watch_path(path, recursive=True)
            
            self.file_monitor.start()
            self.logger.info("文件监控已启动")
            
        except Exception as e:
            self.logger.error(f"启动文件监控失败: {e}")
    
    def _on_directory_selected(self, path: str):
        """处理目录选择事件"""
        self.current_path = path
        if self.file_list:
            self.file_list.set_directory(path)
        
        # 更新状态栏
        if self.main_status_bar:
            self.main_status_bar.set_current_path(path)
        
        # 发射信号
        self.directory_changed.emit(path)
    
    def _on_file_selected(self, file_path: str):
        """处理文件选择事件"""
        if self.preview_panel:
            self.preview_panel.set_file(file_path)
        
        # 发射信号
        self.file_selected.emit(file_path)
    
    def _on_directory_entered(self, path: str):
        """处理目录进入事件"""
        self.current_path = path
        if self.directory_tree:
            self.directory_tree.set_current_path(path)
        
        # 更新状态栏
        if self.main_status_bar:
            self.main_status_bar.set_current_path(path)
    
    def _navigate_to(self, path: str):
        """导航到指定路径"""
        if os.path.exists(path):
            self._on_directory_selected(path)
    
    def _perform_search(self, query: str):
        """执行搜索"""
        if self.file_list:
            self.file_list.search(query)
    
    # 菜单动作处理方法
    def _create_new_folder(self):
        """创建新文件夹"""
        # TODO: 实现新建文件夹对话框
        pass
    
    def _copy_selected(self):
        """复制选中项"""
        # TODO: 实现复制功能
        pass
    
    def _cut_selected(self):
        """剪切选中项"""
        # TODO: 实现剪切功能
        pass
    
    def _paste(self):
        """粘贴"""
        # TODO: 实现粘贴功能
        pass
    
    def _delete_selected(self):
        """删除选中项"""
        # TODO: 实现删除功能
        pass
    
    def _select_all(self):
        """全选"""
        if self.file_list:
            self.file_list.select_all()
    
    def _invert_selection(self):
        """反选"""
        if self.file_list:
            self.file_list.invert_selection()
    
    def _show_search(self):
        """显示搜索"""
        if self.main_toolbar:
            self.main_toolbar.focus_search()
    
    def _toggle_hidden_files(self, checked: bool):
        """切换隐藏文件显示"""
        app_config.set("ui.show_hidden_files", checked)
        if self.file_list:
            self.file_list.set_show_hidden(checked)
    
    def _refresh_view(self):
        """刷新视图"""
        if self.file_list:
            self.file_list.refresh()
        if self.directory_tree:
            self.directory_tree.refresh()
    
    def _show_options(self):
        """显示选项对话框"""
        # TODO: 实现选项对话框
        pass
    
    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "高性能文件管理器 v1.0.0\n\n"
            "基于PySide6的跨平台文件管理应用\n"
            "使用高性能数据结构和算法优化"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存设置
        self._save_settings()
        
        # 停止文件监控
        if self.file_monitor:
            self.file_monitor.stop()
        
        # 接受关闭事件
        event.accept()
        
        self.logger.info("主窗口已关闭")
