# -*- coding: utf-8 -*-
"""
文件操作核心模块 - 提供高性能的文件和目录操作功能
"""

import os
import shutil
import hashlib
import threading
from pathlib import Path
from typing import List, Optional, Callable, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import time

from ..core.logger import get_logger, log_performance
from ..core.config import app_config


class OperationType(Enum):
    """操作类型"""
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    CREATE_DIR = "create_dir"
    RENAME = "rename"


@dataclass
class OperationProgress:
    """操作进度信息"""
    operation_type: OperationType
    current_file: str
    processed_files: int
    total_files: int
    processed_bytes: int
    total_bytes: int
    speed_bps: float  # 字节/秒
    elapsed_time: float
    estimated_time: float
    
    @property
    def progress_percent(self) -> float:
        """进度百分比"""
        if self.total_bytes == 0:
            return 0.0
        return (self.processed_bytes / self.total_bytes) * 100
    
    @property
    def speed_mbps(self) -> float:
        """速度 MB/s"""
        return self.speed_bps / (1024 * 1024)


class FileOperationError(Exception):
    """文件操作异常"""
    pass


class FileOperations:
    """文件操作管理器"""
    
    def __init__(self):
        """初始化文件操作管理器"""
        self.logger = get_logger()
        self._active_operations: Dict[str, bool] = {}
        self._operation_lock = threading.Lock()
        
        # 从配置获取设置
        self.buffer_size = app_config.get("file_operations.copy_buffer_size", 1024 * 1024)
        self.confirm_delete = app_config.get("file_operations.confirm_delete", True)
        self.use_trash = app_config.get("file_operations.use_trash", True)
    
    def _generate_operation_id(self) -> str:
        """生成操作ID"""
        return f"op_{int(time.time() * 1000)}_{threading.current_thread().ident}"
    
    def _register_operation(self, operation_id: str):
        """注册操作"""
        with self._operation_lock:
            self._active_operations[operation_id] = True
    
    def _unregister_operation(self, operation_id: str):
        """注销操作"""
        with self._operation_lock:
            self._active_operations.pop(operation_id, None)
    
    def _is_operation_cancelled(self, operation_id: str) -> bool:
        """检查操作是否被取消"""
        with self._operation_lock:
            return not self._active_operations.get(operation_id, False)
    
    def cancel_operation(self, operation_id: str):
        """取消操作"""
        with self._operation_lock:
            if operation_id in self._active_operations:
                self._active_operations[operation_id] = False
                self.logger.info(f"操作已取消: {operation_id}")
    
    @log_performance
    def copy_file(self, 
                  src_path: str, 
                  dest_path: str,
                  overwrite: bool = False,
                  progress_callback: Optional[Callable[[OperationProgress], None]] = None) -> str:
        """
        复制文件
        
        Args:
            src_path: 源文件路径
            dest_path: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            progress_callback: 进度回调函数
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            src = Path(src_path)
            dest = Path(dest_path)
            
            # 检查源文件
            if not src.exists():
                raise FileOperationError(f"源文件不存在: {src_path}")
            
            # 检查目标文件
            if dest.exists() and not overwrite:
                raise FileOperationError(f"目标文件已存在: {dest_path}")
            
            # 确保目标目录存在
            dest.parent.mkdir(parents=True, exist_ok=True)
            
            # 获取文件大小
            file_size = src.stat().st_size
            
            # 复制文件
            start_time = time.time()
            copied_bytes = 0
            
            with open(src, 'rb') as src_file, open(dest, 'wb') as dest_file:
                while True:
                    if self._is_operation_cancelled(operation_id):
                        dest.unlink(missing_ok=True)  # 删除部分复制的文件
                        raise FileOperationError("操作已取消")
                    
                    chunk = src_file.read(self.buffer_size)
                    if not chunk:
                        break
                    
                    dest_file.write(chunk)
                    copied_bytes += len(chunk)
                    
                    # 更新进度
                    if progress_callback:
                        elapsed = time.time() - start_time
                        speed = copied_bytes / elapsed if elapsed > 0 else 0
                        remaining_bytes = file_size - copied_bytes
                        eta = remaining_bytes / speed if speed > 0 else 0
                        
                        progress = OperationProgress(
                            operation_type=OperationType.COPY,
                            current_file=str(src),
                            processed_files=0,
                            total_files=1,
                            processed_bytes=copied_bytes,
                            total_bytes=file_size,
                            speed_bps=speed,
                            elapsed_time=elapsed,
                            estimated_time=eta
                        )
                        progress_callback(progress)
            
            # 复制文件属性
            shutil.copystat(src, dest)
            
            self.logger.info(f"文件复制完成: {src_path} -> {dest_path}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"文件复制失败: {e}")
            raise FileOperationError(f"复制失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    @log_performance
    def copy_directory(self,
                      src_path: str,
                      dest_path: str,
                      overwrite: bool = False,
                      progress_callback: Optional[Callable[[OperationProgress], None]] = None) -> str:
        """
        复制目录
        
        Args:
            src_path: 源目录路径
            dest_path: 目标目录路径
            overwrite: 是否覆盖已存在的文件
            progress_callback: 进度回调函数
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            src = Path(src_path)
            dest = Path(dest_path)
            
            if not src.exists() or not src.is_dir():
                raise FileOperationError(f"源目录不存在: {src_path}")
            
            # 计算总大小和文件数
            total_size = 0
            total_files = 0
            file_list = []
            
            for file_path in src.rglob('*'):
                if file_path.is_file():
                    file_list.append(file_path)
                    total_size += file_path.stat().st_size
                    total_files += 1
            
            # 开始复制
            start_time = time.time()
            processed_bytes = 0
            processed_files = 0
            
            for file_path in file_list:
                if self._is_operation_cancelled(operation_id):
                    raise FileOperationError("操作已取消")
                
                # 计算相对路径
                rel_path = file_path.relative_to(src)
                dest_file = dest / rel_path
                
                # 确保目标目录存在
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                if dest_file.exists() and not overwrite:
                    self.logger.warning(f"跳过已存在的文件: {dest_file}")
                    continue
                
                file_size = file_path.stat().st_size
                shutil.copy2(file_path, dest_file)
                
                processed_bytes += file_size
                processed_files += 1
                
                # 更新进度
                if progress_callback:
                    elapsed = time.time() - start_time
                    speed = processed_bytes / elapsed if elapsed > 0 else 0
                    remaining_bytes = total_size - processed_bytes
                    eta = remaining_bytes / speed if speed > 0 else 0
                    
                    progress = OperationProgress(
                        operation_type=OperationType.COPY,
                        current_file=str(file_path),
                        processed_files=processed_files,
                        total_files=total_files,
                        processed_bytes=processed_bytes,
                        total_bytes=total_size,
                        speed_bps=speed,
                        elapsed_time=elapsed,
                        estimated_time=eta
                    )
                    progress_callback(progress)
            
            self.logger.info(f"目录复制完成: {src_path} -> {dest_path}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"目录复制失败: {e}")
            raise FileOperationError(f"复制失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    @log_performance
    def move_file(self, src_path: str, dest_path: str, overwrite: bool = False) -> str:
        """
        移动文件
        
        Args:
            src_path: 源文件路径
            dest_path: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            src = Path(src_path)
            dest = Path(dest_path)
            
            if not src.exists():
                raise FileOperationError(f"源文件不存在: {src_path}")
            
            if dest.exists() and not overwrite:
                raise FileOperationError(f"目标文件已存在: {dest_path}")
            
            # 确保目标目录存在
            dest.parent.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            shutil.move(src, dest)
            
            self.logger.info(f"文件移动完成: {src_path} -> {dest_path}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"文件移动失败: {e}")
            raise FileOperationError(f"移动失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    @log_performance
    def delete_file(self, file_path: str, use_trash: Optional[bool] = None) -> str:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            use_trash: 是否使用回收站，None表示使用配置设置
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise FileOperationError(f"文件不存在: {file_path}")
            
            use_trash_setting = use_trash if use_trash is not None else self.use_trash
            
            if use_trash_setting:
                # 使用系统回收站
                self._move_to_trash(path)
            else:
                # 永久删除
                if path.is_dir():
                    shutil.rmtree(path)
                else:
                    path.unlink()
            
            self.logger.info(f"文件删除完成: {file_path}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"文件删除失败: {e}")
            raise FileOperationError(f"删除失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    def _move_to_trash(self, path: Path):
        """移动文件到回收站"""
        try:
            # 尝试使用send2trash库
            import send2trash
            send2trash.send2trash(str(path))
        except ImportError:
            # 如果没有send2trash，使用系统命令
            import platform
            system = platform.system()
            
            if system == "Windows":
                os.system(f'move "{path}" "%USERPROFILE%\\Recycle Bin"')
            elif system == "Darwin":  # macOS
                os.system(f'mv "{path}" ~/.Trash/')
            else:  # Linux
                trash_dir = Path.home() / ".local/share/Trash/files"
                trash_dir.mkdir(parents=True, exist_ok=True)
                shutil.move(path, trash_dir / path.name)
    
    @log_performance
    def create_directory(self, dir_path: str, parents: bool = True) -> str:
        """
        创建目录
        
        Args:
            dir_path: 目录路径
            parents: 是否创建父目录
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            path = Path(dir_path)
            path.mkdir(parents=parents, exist_ok=True)
            
            self.logger.info(f"目录创建完成: {dir_path}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"目录创建失败: {e}")
            raise FileOperationError(f"创建失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    @log_performance
    def rename_file(self, old_path: str, new_name: str) -> str:
        """
        重命名文件
        
        Args:
            old_path: 原文件路径
            new_name: 新文件名
            
        Returns:
            操作ID
        """
        operation_id = self._generate_operation_id()
        self._register_operation(operation_id)
        
        try:
            old = Path(old_path)
            new = old.parent / new_name
            
            if not old.exists():
                raise FileOperationError(f"文件不存在: {old_path}")
            
            if new.exists():
                raise FileOperationError(f"目标文件已存在: {new}")
            
            old.rename(new)
            
            self.logger.info(f"文件重命名完成: {old_path} -> {new}")
            return operation_id
            
        except Exception as e:
            self.logger.error(f"文件重命名失败: {e}")
            raise FileOperationError(f"重命名失败: {e}")
        finally:
            self._unregister_operation(operation_id)
    
    def get_file_checksum(self, file_path: str, algorithm: str = "md5") -> str:
        """
        计算文件校验和
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            文件校验和
        """
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            while chunk := f.read(self.buffer_size):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    def get_active_operations(self) -> List[str]:
        """获取活动操作列表"""
        with self._operation_lock:
            return [op_id for op_id, active in self._active_operations.items() if active]
